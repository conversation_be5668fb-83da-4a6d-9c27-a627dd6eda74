.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
  padding: 40px 20px;
  margin-bottom: 30px;
}

.app-header h1 {
  font-size: 3rem;
  margin: 0 0 10px 0;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.app-header p {
  font-size: 1.2rem;
  margin: 0;
  opacity: 0.9;
}

.main-content {
  flex: 1;
  padding: 0 20px;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.loading {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading p {
  font-size: 1.1rem;
  font-weight: 500;
}

.app-footer {
  background: #f8f9fa;
  text-align: center;
  padding: 30px 20px;
  margin-top: 50px;
  border-top: 1px solid #e0e0e0;
  color: #666;
}

.app-footer p {
  margin: 0;
  font-size: 0.9rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .app-header {
    padding: 30px 15px;
  }

  .app-header h1 {
    font-size: 2.2rem;
  }

  .app-header p {
    font-size: 1rem;
  }

  .main-content {
    padding: 0 15px;
  }
}