# CityUnfold Development Startup Script
# This script starts both the server and client in development mode

Write-Host "🚀 Starting CityUnfold Development Environment..." -ForegroundColor Green
Write-Host ""

# Function to check if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check if Node.js is installed
if (-not (Test-Command "node")) {
    Write-Host "❌ Node.js is not installed. Please install Node.js from https://nodejs.org/" -ForegroundColor Red
    exit 1
}

# Check if npm is installed
if (-not (Test-Command "npm")) {
    Write-Host "❌ npm is not installed. Please install npm." -ForegroundColor Red
    exit 1
}

Write-Host "✅ Node.js and npm are installed" -ForegroundColor Green

# Check if dependencies are installed in server
if (-not (Test-Path "server/node_modules")) {
    Write-Host "📦 Installing server dependencies..." -ForegroundColor Yellow
    Set-Location server
    npm install
    Set-Location ..
    Write-Host "✅ Server dependencies installed" -ForegroundColor Green
} else {
    Write-Host "✅ Server dependencies already installed" -ForegroundColor Green
}

# Check if dependencies are installed in client
if (-not (Test-Path "client/node_modules")) {
    Write-Host "📦 Installing client dependencies..." -ForegroundColor Yellow
    Set-Location client
    npm install
    Set-Location ..
    Write-Host "✅ Client dependencies installed" -ForegroundColor Green
} else {
    Write-Host "✅ Client dependencies already installed" -ForegroundColor Green
}

# Check if axios is installed in client
if (-not (Test-Path "client/node_modules/axios")) {
    Write-Host "📦 Installing missing axios dependency..." -ForegroundColor Yellow
    Set-Location client
    npm install axios
    Set-Location ..
    Write-Host "✅ Axios installed" -ForegroundColor Green
}

Write-Host ""
Write-Host "🎯 Starting development servers..." -ForegroundColor Cyan
Write-Host "   📍 Server will run on: http://localhost:5000" -ForegroundColor White
Write-Host "   📍 Client will run on: http://localhost:5173" -ForegroundColor White
Write-Host ""
Write-Host "💡 Note: This project uses dummy data when APIs are not configured" -ForegroundColor Yellow
Write-Host "   - MongoDB: Uses sample cities, places, and restaurants" -ForegroundColor White
Write-Host "   - YouTube API: Uses mock video data" -ForegroundColor White
Write-Host ""
Write-Host "🔧 To configure real APIs, edit server/.env file" -ForegroundColor Cyan
Write-Host ""

# Start server in background
Write-Host "🚀 Starting server..." -ForegroundColor Green
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd server; npm run dev" -WindowStyle Normal

# Wait a moment for server to start
Start-Sleep -Seconds 3

# Start client
Write-Host "🚀 Starting client..." -ForegroundColor Green
Set-Location client
npm run dev
