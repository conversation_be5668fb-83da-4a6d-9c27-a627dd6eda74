.about-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 20px;
}

.about-container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.about-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
  padding: 60px 40px;
}

.about-header h1 {
  font-size: 3rem;
  margin-bottom: 15px;
  font-weight: 700;
}

.about-subtitle {
  font-size: 1.3rem;
  opacity: 0.9;
  font-weight: 300;
}

.about-content {
  padding: 60px 40px;
}

.about-section {
  margin-bottom: 50px;
  text-align: center;
}

.about-section:last-child {
  margin-bottom: 0;
}

.section-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

.about-section h2 {
  font-size: 2.2rem;
  color: #333;
  margin-bottom: 20px;
  font-weight: 600;
}

.about-section p {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #666;
  max-width: 800px;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.feature-card {
  background: #f8f9fa;
  padding: 30px 25px;
  border-radius: 15px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #e9ecef;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.feature-card h3 {
  font-size: 1.3rem;
  color: #333;
  margin-bottom: 15px;
  font-weight: 600;
}

.feature-card p {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.benefits-list {
  text-align: left;
  max-width: 600px;
  margin: 30px auto 0;
  list-style: none;
  padding: 0;
}

.benefits-list li {
  padding: 15px 0;
  border-bottom: 1px solid #e9ecef;
  font-size: 1.1rem;
  line-height: 1.6;
  color: #555;
}

.benefits-list li:last-child {
  border-bottom: none;
}

.benefits-list strong {
  color: #667eea;
  font-weight: 600;
}

.cta-section {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  padding: 50px 40px;
  border-radius: 20px;
  margin-top: 40px;
}

.cta-section h2 {
  color: #667eea;
  margin-bottom: 20px;
}

.cta-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px 40px;
  border-radius: 25px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 20px;
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* Responsive design */
@media (max-width: 768px) {
  .about-page {
    padding: 20px 10px;
  }
  
  .about-header {
    padding: 40px 20px;
  }
  
  .about-header h1 {
    font-size: 2.2rem;
  }
  
  .about-subtitle {
    font-size: 1.1rem;
  }
  
  .about-content {
    padding: 40px 20px;
  }
  
  .about-section h2 {
    font-size: 1.8rem;
  }
  
  .about-section p {
    font-size: 1rem;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .feature-card {
    padding: 25px 20px;
  }
  
  .cta-section {
    padding: 40px 20px;
    margin-top: 30px;
  }
  
  .cta-button {
    padding: 12px 30px;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .about-header h1 {
    font-size: 1.8rem;
  }
  
  .about-subtitle {
    font-size: 1rem;
  }
  
  .section-icon {
    font-size: 2.5rem;
  }
  
  .about-section h2 {
    font-size: 1.5rem;
  }
}
