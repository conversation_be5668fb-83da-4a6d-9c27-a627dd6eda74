import { City, Place, Restaurant } from '../models/index.js';

// Get all cities
export const getAllCities = async (req, res) => {
  try {
    const cities = await City.find({ isActive: true })
      .select('name description state country coordinates image')
      .sort({ name: 1 });
    
    res.json({
      success: true,
      count: cities.length,
      data: cities
    });
  } catch (error) {
    console.error('Error fetching cities:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching cities',
      error: error.message
    });
  }
};

// Get city by name
export const getCityByName = async (req, res) => {
  try {
    const { name } = req.params;
    const city = await City.findOne({ 
      name: { $regex: new RegExp(name, 'i') }, 
      isActive: true 
    });

    if (!city) {
      return res.status(404).json({
        success: false,
        message: `City '${name}' not found`
      });
    }

    res.json({
      success: true,
      data: city
    });
  } catch (error) {
    console.error('Error fetching city:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching city',
      error: error.message
    });
  }
};

// Get places for a city
export const getCityPlaces = async (req, res) => {
  try {
    const { name } = req.params;
    const { category, limit = 10 } = req.query;

    // Build query
    const query = { 
      city: { $regex: new RegExp(name, 'i') }, 
      isActive: true 
    };
    
    if (category) {
      query.category = category;
    }

    const places = await Place.find(query)
      .sort({ rating: -1, name: 1 })
      .limit(parseInt(limit));

    res.json({
      success: true,
      city: name,
      count: places.length,
      data: places
    });
  } catch (error) {
    console.error('Error fetching places:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching places',
      error: error.message
    });
  }
};

// Get restaurants for a city
export const getCityRestaurants = async (req, res) => {
  try {
    const { name } = req.params;
    const { type, priceRange, limit = 10 } = req.query;

    // Build query
    const query = { 
      city: { $regex: new RegExp(name, 'i') }, 
      isActive: true 
    };
    
    if (type) {
      query.type = { $regex: new RegExp(type, 'i') };
    }
    
    if (priceRange) {
      query.priceRange = priceRange;
    }

    const restaurants = await Restaurant.find(query)
      .sort({ rating: -1, name: 1 })
      .limit(parseInt(limit));

    res.json({
      success: true,
      city: name,
      count: restaurants.length,
      data: restaurants
    });
  } catch (error) {
    console.error('Error fetching restaurants:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching restaurants',
      error: error.message
    });
  }
};

// Get culture information for a city
export const getCityCulture = async (req, res) => {
  try {
    const { name } = req.params;
    const city = await City.findOne({ 
      name: { $regex: new RegExp(name, 'i') }, 
      isActive: true 
    }).select('name culture description');

    if (!city) {
      return res.status(404).json({
        success: false,
        message: `City '${name}' not found`
      });
    }

    res.json({
      success: true,
      city: city.name,
      data: {
        culture: city.culture,
        description: city.description
      }
    });
  } catch (error) {
    console.error('Error fetching culture info:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching culture information',
      error: error.message
    });
  }
};

// Search cities
export const searchCities = async (req, res) => {
  try {
    const { q, limit = 5 } = req.query;
    
    if (!q) {
      return res.status(400).json({
        success: false,
        message: 'Search query is required'
      });
    }

    const cities = await City.find({
      $and: [
        { isActive: true },
        {
          $or: [
            { name: { $regex: new RegExp(q, 'i') } },
            { description: { $regex: new RegExp(q, 'i') } },
            { state: { $regex: new RegExp(q, 'i') } }
          ]
        }
      ]
    })
    .select('name description state coordinates')
    .limit(parseInt(limit))
    .sort({ name: 1 });

    res.json({
      success: true,
      query: q,
      count: cities.length,
      data: cities
    });
  } catch (error) {
    console.error('Error searching cities:', error);
    res.status(500).json({
      success: false,
      message: 'Error searching cities',
      error: error.message
    });
  }
};
