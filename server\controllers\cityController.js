import { City, Place, Restaurant } from '../models/index.js';
import { sampleCities, samplePlaces, sampleRestaurants } from '../seeds/sampleData.js';
import mongoose from 'mongoose';

// Get all cities
export const getAllCities = async (req, res) => {
  try {
    // Check if database is connected
    if (mongoose.connection.readyState !== 1) {
      console.log('📦 Using sample data - Database not connected');
      return res.json({
        success: true,
        count: sampleCities.length,
        data: sampleCities.map(city => ({ ...city, isActive: true })),
        note: 'Sample data - Database not connected'
      });
    }

    const cities = await City.find({ isActive: true })
      .select('name description state country coordinates image')
      .sort({ name: 1 });

    res.json({
      success: true,
      count: cities.length,
      data: cities
    });
  } catch (error) {
    console.error('Error fetching cities:', error);
    // Fallback to sample data on error
    console.log('📦 Using sample data - Database error fallback');
    res.json({
      success: true,
      count: sampleCities.length,
      data: sampleCities.map(city => ({ ...city, isActive: true })),
      note: 'Sample data - Database error fallback'
    });
  }
};

// Get city by name
export const getCityByName = async (req, res) => {
  try {
    const { name } = req.params;

    // Check if database is connected
    if (mongoose.connection.readyState !== 1) {
      console.log('📦 Using sample data - Database not connected');
      const city = sampleCities.find(c =>
        c.name.toLowerCase().includes(name.toLowerCase())
      );

      if (!city) {
        return res.status(404).json({
          success: false,
          message: `City '${name}' not found in sample data`
        });
      }

      return res.json({
        success: true,
        data: { ...city, isActive: true },
        note: 'Sample data - Database not connected'
      });
    }

    const city = await City.findOne({
      name: { $regex: new RegExp(name, 'i') },
      isActive: true
    });

    if (!city) {
      return res.status(404).json({
        success: false,
        message: `City '${name}' not found`
      });
    }

    res.json({
      success: true,
      data: city
    });
  } catch (error) {
    console.error('Error fetching city:', error);
    // Fallback to sample data
    console.log('📦 Using sample data - Database error fallback');
    const city = sampleCities.find(c =>
      c.name.toLowerCase().includes(name.toLowerCase())
    );

    if (!city) {
      return res.status(404).json({
        success: false,
        message: `City '${name}' not found`
      });
    }

    res.json({
      success: true,
      data: { ...city, isActive: true },
      note: 'Sample data - Database error fallback'
    });
  }
};

// Get places for a city
export const getCityPlaces = async (req, res) => {
  try {
    const { name } = req.params;
    const { category, limit = 10 } = req.query;

    // Check if database is connected
    if (mongoose.connection.readyState !== 1) {
      console.log('📦 Using sample data - Database not connected');
      let places = samplePlaces.filter(place =>
        place.city.toLowerCase().includes(name.toLowerCase())
      );

      if (category) {
        places = places.filter(place => place.category === category);
      }

      places = places
        .sort((a, b) => b.rating - a.rating)
        .slice(0, parseInt(limit))
        .map(place => ({ ...place, isActive: true }));

      return res.json({
        success: true,
        city: name,
        count: places.length,
        data: places,
        note: 'Sample data - Database not connected'
      });
    }

    // Build query
    const query = {
      city: { $regex: new RegExp(name, 'i') },
      isActive: true
    };

    if (category) {
      query.category = category;
    }

    const places = await Place.find(query)
      .sort({ rating: -1, name: 1 })
      .limit(parseInt(limit));

    res.json({
      success: true,
      city: name,
      count: places.length,
      data: places
    });
  } catch (error) {
    console.error('Error fetching places:', error);
    // Fallback to sample data
    console.log('📦 Using sample data - Database error fallback');
    let places = samplePlaces.filter(place =>
      place.city.toLowerCase().includes(name.toLowerCase())
    );

    if (category) {
      places = places.filter(place => place.category === category);
    }

    places = places
      .sort((a, b) => b.rating - a.rating)
      .slice(0, parseInt(limit))
      .map(place => ({ ...place, isActive: true }));

    res.json({
      success: true,
      city: name,
      count: places.length,
      data: places,
      note: 'Sample data - Database error fallback'
    });
  }
};

// Get restaurants for a city
export const getCityRestaurants = async (req, res) => {
  try {
    const { name } = req.params;
    const { type, priceRange, limit = 10 } = req.query;

    // Check if database is connected
    if (mongoose.connection.readyState !== 1) {
      console.log('📦 Using sample data - Database not connected');
      let restaurants = sampleRestaurants.filter(restaurant =>
        restaurant.city.toLowerCase().includes(name.toLowerCase())
      );

      if (type) {
        restaurants = restaurants.filter(restaurant =>
          restaurant.type.toLowerCase().includes(type.toLowerCase())
        );
      }

      if (priceRange) {
        restaurants = restaurants.filter(restaurant =>
          restaurant.priceRange === priceRange
        );
      }

      restaurants = restaurants
        .sort((a, b) => b.rating - a.rating)
        .slice(0, parseInt(limit))
        .map(restaurant => ({ ...restaurant, isActive: true }));

      return res.json({
        success: true,
        city: name,
        count: restaurants.length,
        data: restaurants,
        note: 'Sample data - Database not connected'
      });
    }

    // Build query
    const query = {
      city: { $regex: new RegExp(name, 'i') },
      isActive: true
    };

    if (type) {
      query.type = { $regex: new RegExp(type, 'i') };
    }

    if (priceRange) {
      query.priceRange = priceRange;
    }

    const restaurants = await Restaurant.find(query)
      .sort({ rating: -1, name: 1 })
      .limit(parseInt(limit));

    res.json({
      success: true,
      city: name,
      count: restaurants.length,
      data: restaurants
    });
  } catch (error) {
    console.error('Error fetching restaurants:', error);
    // Fallback to sample data
    console.log('📦 Using sample data - Database error fallback');
    let restaurants = sampleRestaurants.filter(restaurant =>
      restaurant.city.toLowerCase().includes(name.toLowerCase())
    );

    if (type) {
      restaurants = restaurants.filter(restaurant =>
        restaurant.type.toLowerCase().includes(type.toLowerCase())
      );
    }

    if (priceRange) {
      restaurants = restaurants.filter(restaurant =>
        restaurant.priceRange === priceRange
      );
    }

    restaurants = restaurants
      .sort((a, b) => b.rating - a.rating)
      .slice(0, parseInt(limit))
      .map(restaurant => ({ ...restaurant, isActive: true }));

    res.json({
      success: true,
      city: name,
      count: restaurants.length,
      data: restaurants,
      note: 'Sample data - Database error fallback'
    });
  }
};

// Get culture information for a city
export const getCityCulture = async (req, res) => {
  try {
    const { name } = req.params;

    // Check if database is connected
    if (mongoose.connection.readyState !== 1) {
      console.log('📦 Using sample data - Database not connected');
      const city = sampleCities.find(c =>
        c.name.toLowerCase().includes(name.toLowerCase())
      );

      if (!city) {
        return res.status(404).json({
          success: false,
          message: `City '${name}' not found in sample data`
        });
      }

      return res.json({
        success: true,
        city: city.name,
        data: {
          culture: city.culture,
          description: city.description
        },
        note: 'Sample data - Database not connected'
      });
    }

    const city = await City.findOne({
      name: { $regex: new RegExp(name, 'i') },
      isActive: true
    }).select('name culture description');

    if (!city) {
      return res.status(404).json({
        success: false,
        message: `City '${name}' not found`
      });
    }

    res.json({
      success: true,
      city: city.name,
      data: {
        culture: city.culture,
        description: city.description
      }
    });
  } catch (error) {
    console.error('Error fetching culture info:', error);
    // Fallback to sample data
    console.log('📦 Using sample data - Database error fallback');
    const city = sampleCities.find(c =>
      c.name.toLowerCase().includes(name.toLowerCase())
    );

    if (!city) {
      return res.status(404).json({
        success: false,
        message: `City '${name}' not found`
      });
    }

    res.json({
      success: true,
      city: city.name,
      data: {
        culture: city.culture,
        description: city.description
      },
      note: 'Sample data - Database error fallback'
    });
  }
};

// Search cities
export const searchCities = async (req, res) => {
  try {
    const { q, limit = 5 } = req.query;

    if (!q) {
      return res.status(400).json({
        success: false,
        message: 'Search query is required'
      });
    }

    // Check if database is connected
    if (mongoose.connection.readyState !== 1) {
      console.log('📦 Using sample data - Database not connected');
      const cities = sampleCities
        .filter(city =>
          city.name.toLowerCase().includes(q.toLowerCase()) ||
          city.description.toLowerCase().includes(q.toLowerCase()) ||
          city.state.toLowerCase().includes(q.toLowerCase())
        )
        .slice(0, parseInt(limit))
        .map(city => ({
          name: city.name,
          description: city.description,
          state: city.state,
          coordinates: city.coordinates
        }));

      return res.json({
        success: true,
        query: q,
        count: cities.length,
        data: cities,
        note: 'Sample data - Database not connected'
      });
    }

    const cities = await City.find({
      $and: [
        { isActive: true },
        {
          $or: [
            { name: { $regex: new RegExp(q, 'i') } },
            { description: { $regex: new RegExp(q, 'i') } },
            { state: { $regex: new RegExp(q, 'i') } }
          ]
        }
      ]
    })
    .select('name description state coordinates')
    .limit(parseInt(limit))
    .sort({ name: 1 });

    res.json({
      success: true,
      query: q,
      count: cities.length,
      data: cities
    });
  } catch (error) {
    console.error('Error searching cities:', error);
    // Fallback to sample data
    console.log('📦 Using sample data - Database error fallback');
    const cities = sampleCities
      .filter(city =>
        city.name.toLowerCase().includes(q.toLowerCase()) ||
        city.description.toLowerCase().includes(q.toLowerCase()) ||
        city.state.toLowerCase().includes(q.toLowerCase())
      )
      .slice(0, parseInt(limit))
      .map(city => ({
        name: city.name,
        description: city.description,
        state: city.state,
        coordinates: city.coordinates
      }));

    res.json({
      success: true,
      query: q,
      count: cities.length,
      data: cities,
      note: 'Sample data - Database error fallback'
    });
  }
};
