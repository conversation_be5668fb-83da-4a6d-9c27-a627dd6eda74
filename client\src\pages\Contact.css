.contact-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 20px;
}

.contact-container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.contact-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
  padding: 60px 40px;
}

.contact-header h1 {
  font-size: 3rem;
  margin-bottom: 15px;
  font-weight: 700;
}

.contact-subtitle {
  font-size: 1.3rem;
  opacity: 0.9;
  font-weight: 300;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  padding: 60px 40px;
}

.contact-info h2,
.contact-form-section h2 {
  font-size: 2rem;
  color: #333;
  margin-bottom: 30px;
  font-weight: 600;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 30px;
}

.info-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9ff;
  border-radius: 10px;
  flex-shrink: 0;
}

.info-details h3 {
  font-size: 1.2rem;
  color: #333;
  margin-bottom: 5px;
  font-weight: 600;
}

.info-details p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.social-links {
  margin-top: 40px;
}

.social-links h3 {
  font-size: 1.2rem;
  color: #333;
  margin-bottom: 15px;
  font-weight: 600;
}

.social-icons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.social-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.social-link:hover {
  color: #5a6fd8;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  font-size: 0.95rem;
}

.form-group input,
.form-group textarea {
  padding: 12px 15px;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.submit-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 10px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-top: 10px;
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.submit-button.submitting {
  background: #6c757d;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.faq-section {
  background: #f8f9fa;
  padding: 50px 40px;
  margin-top: 40px;
}

.faq-section h2 {
  text-align: center;
  font-size: 2rem;
  color: #333;
  margin-bottom: 40px;
  font-weight: 600;
}

.faq-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
}

.faq-item {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.faq-item h3 {
  font-size: 1.2rem;
  color: #333;
  margin-bottom: 15px;
  font-weight: 600;
}

.faq-item p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .contact-page {
    padding: 20px 10px;
  }
  
  .contact-header {
    padding: 40px 20px;
  }
  
  .contact-header h1 {
    font-size: 2.2rem;
  }
  
  .contact-subtitle {
    font-size: 1.1rem;
  }
  
  .contact-content {
    grid-template-columns: 1fr;
    gap: 40px;
    padding: 40px 20px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .social-icons {
    flex-direction: row;
    flex-wrap: wrap;
  }
  
  .faq-section {
    padding: 40px 20px;
  }
  
  .faq-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .contact-header h1 {
    font-size: 1.8rem;
  }
  
  .contact-subtitle {
    font-size: 1rem;
  }
  
  .contact-info h2,
  .contact-form-section h2 {
    font-size: 1.5rem;
  }
  
  .faq-section h2 {
    font-size: 1.5rem;
  }
}
