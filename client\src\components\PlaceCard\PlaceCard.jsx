import './PlaceCard.css';

const PlaceCard = ({ place }) => {
  const getCategoryIcon = (category) => {
    const icons = {
      temple: '🛕',
      monument: '🏛️',
      museum: '🏛️',
      park: '🌳',
      market: '🛒',
      heritage: '🏺',
      nature: '🌿',
      other: '📍'
    };
    return icons[category] || icons.other;
  };

  const getRatingStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push('⭐');
    }
    if (hasHalfStar) {
      stars.push('⭐');
    }
    return stars.join('');
  };

  return (
    <div className="place-card">
      <div className="place-image">
        {place.image ? (
          <img src={place.image} alt={place.name} />
        ) : (
          <div className="placeholder-image">
            <span className="category-icon">{getCategoryIcon(place.category)}</span>
          </div>
        )}
        <div className="place-category">
          {getCategoryIcon(place.category)} {place.category || 'Place'}
        </div>
      </div>
      
      <div className="place-content">
        <h3 className="place-name">{place.name}</h3>
        <p className="place-description">{place.description}</p>
        
        <div className="place-details">
          {place.rating > 0 && (
            <div className="place-rating">
              <span className="rating-stars">{getRatingStars(place.rating)}</span>
              <span className="rating-value">{place.rating}</span>
            </div>
          )}
          
          {place.entryFee && (
            <div className="entry-fee">
              <span className="fee-label">Entry:</span>
              <span className="fee-value">{place.entryFee}</span>
            </div>
          )}
        </div>
        
        {place.visitingHours && (
          <div className="visiting-hours">
            <span className="hours-icon">🕒</span>
            <span>{place.visitingHours.open} - {place.visitingHours.close}</span>
            {place.visitingHours.note && (
              <div className="hours-note">{place.visitingHours.note}</div>
            )}
          </div>
        )}
        
        {place.location?.address && (
          <div className="place-address">
            <span className="address-icon">📍</span>
            <span>{place.location.address}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default PlaceCard;
