import express from 'express';
import {
  getAllCities,
  getCityByName,
  getCityPlaces,
  getCityRestaurants,
  getCityCulture,
  searchCities
} from '../controllers/cityController.js';

const router = express.Router();

// GET /api/cities - Get all cities
router.get('/', getAllCities);

// GET /api/cities/search?q=query - Search cities
router.get('/search', searchCities);

// GET /api/cities/:name - Get specific city
router.get('/:name', getCityByName);

// GET /api/cities/:name/places - Get places in a city
router.get('/:name/places', getCityPlaces);

// GET /api/cities/:name/restaurants - Get restaurants in a city
router.get('/:name/restaurants', getCityRestaurants);

// GET /api/cities/:name/culture - Get culture info for a city
router.get('/:name/culture', getCityCulture);

export default router;
