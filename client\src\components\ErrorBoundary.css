.error-boundary {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%);
  color: white;
  padding: 20px;
}

.error-content {
  text-align: center;
  max-width: 600px;
  background: rgba(255, 255, 255, 0.1);
  padding: 40px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.error-content h1 {
  font-size: 2.5rem;
  margin-bottom: 20px;
  color: white;
}

.error-content p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  color: rgba(255, 255, 255, 0.9);
}

.error-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.reload-button,
.retry-button {
  padding: 12px 24px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
}

.reload-button {
  background: white;
  color: #ff6b6b;
}

.reload-button:hover {
  background: #f0f0f0;
  transform: translateY(-2px);
}

.retry-button {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid white;
}

.retry-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.error-details {
  text-align: left;
  margin-top: 20px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  padding: 20px;
}

.error-details summary {
  cursor: pointer;
  font-weight: 600;
  margin-bottom: 10px;
  color: white;
}

.error-stack {
  background: rgba(0, 0, 0, 0.3);
  padding: 15px;
  border-radius: 5px;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  color: #ffcccc;
}

/* Responsive design */
@media (max-width: 768px) {
  .error-content {
    padding: 30px 20px;
  }
  
  .error-content h1 {
    font-size: 2rem;
  }
  
  .error-content p {
    font-size: 1rem;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .reload-button,
  .retry-button {
    width: 100%;
    max-width: 200px;
  }
}
