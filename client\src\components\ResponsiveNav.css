.responsive-nav {
  width: 100%;
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  overflow: hidden;
}

/* Desktop Navigation */
.nav-desktop {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.nav-tabs {
  display: flex;
  flex: 1;
}

.nav-tab {
  flex: 1;
  padding: 20px 15px;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
  font-size: 16px;
  font-weight: 500;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
}

.nav-tab:hover {
  background: #f8f9fa;
  color: #333;
}

.nav-tab.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
}

.tab-label {
  white-space: nowrap;
}

.tab-count {
  font-size: 12px;
  color: #999;
  font-weight: 400;
}

.nav-tab.active .tab-count {
  color: #667eea;
}

.filter-toggle-btn {
  padding: 15px 20px;
  border: none;
  background: #667eea;
  color: white;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 0;
}

.filter-toggle-btn:hover {
  background: #5a6fd8;
}

/* Mobile Navigation */
.nav-mobile {
  display: none;
}

.nav-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.current-tab {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-toggle-btn.mobile {
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 16px;
  min-width: auto;
}

.menu-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  display: flex;
  flex-direction: column;
  gap: 3px;
  width: 24px;
  height: 18px;
}

.menu-toggle span {
  width: 100%;
  height: 2px;
  background: #333;
  transition: all 0.3s ease;
  border-radius: 1px;
}

.menu-toggle.open span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.menu-toggle.open span:nth-child(2) {
  opacity: 0;
}

.menu-toggle.open span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

.nav-dropdown {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  background: white;
}

.nav-dropdown.open {
  max-height: 300px;
}

.nav-tab-mobile {
  width: 100%;
  padding: 15px 20px;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid #f0f0f0;
  font-size: 16px;
  font-weight: 500;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: left;
}

.nav-tab-mobile:hover {
  background: #f8f9fa;
  color: #333;
}

.nav-tab-mobile.active {
  color: #667eea;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border-left: 3px solid #667eea;
}

.nav-tab-mobile:last-child {
  border-bottom: none;
}

/* Responsive breakpoints */
@media (max-width: 768px) {
  .nav-desktop {
    display: none;
  }
  
  .nav-mobile {
    display: block;
  }
  
  .responsive-nav {
    margin-bottom: 15px;
  }
}

@media (max-width: 480px) {
  .nav-header {
    padding: 12px 15px;
  }
  
  .current-tab {
    font-size: 14px;
  }
  
  .nav-tab-mobile {
    padding: 12px 15px;
    font-size: 14px;
  }
  
  .filter-toggle-btn.mobile {
    padding: 6px 10px;
    font-size: 14px;
  }
}

/* Animation for smooth transitions */
@media (prefers-reduced-motion: no-preference) {
  .nav-tab,
  .nav-tab-mobile,
  .filter-toggle-btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .nav-dropdown {
    transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}
