import express from 'express';
import cityRoutes from './cityRoutes.js';
import youtubeRoutes from './youtubeRoutes.js';

const router = express.Router();

// API Routes
router.use('/cities', cityRoutes);
router.use('/vlogs', youtubeRoutes);

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'CityUnfold API is healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

export default router;
