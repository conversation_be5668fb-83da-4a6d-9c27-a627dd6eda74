@echo off
echo.
echo 🔧 Fixing CityUnfold Dependencies...
echo.

echo 📦 Installing server dependencies...
cd server
npm install
cd ..

echo 📦 Installing client dependencies...
cd client
npm install

echo 📦 Ensuring axios is installed...
npm install axios

echo 📦 Ensuring all required dependencies are present...
npm list axios >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Axios installation failed, trying again...
    npm install axios --force
) else (
    echo ✅ Axios is properly installed
)

cd ..

echo.
echo ✅ All dependencies fixed!
echo 🚀 You can now run: .\start-dev.bat
echo.
pause
