# CityUnfold 🌍

A comprehensive travel website where users can explore cities and discover top tourist places, restaurants, YouTube vlogs, maps, and cultural information.

## 🚀 Project Structure

```
CityUnfold/
├── client/          # React frontend (Vite)
│   ├── src/
│   ├── public/
│   └── package.json
├── server/          # Express backend
│   ├── controllers/ # API logic
│   ├── models/      # MongoDB schemas
│   ├── routes/      # API routes
│   ├── seeds/       # Sample data
│   └── package.json
└── README.md
```

## 🛠️ Tech Stack

### Frontend
- **React** with Vite
- **CSS/Tailwind** for styling
- **Axios** for API calls

### Backend
- **Node.js** with Express
- **MongoDB** with Mongoose
- **YouTube Data API** for vlogs
- **Google Maps API** for maps

## 🏃‍♂️ Quick Start

### Prerequisites
- Node.js (v16 or higher)
- MongoDB Atlas account (optional - uses dummy data if not configured)
- YouTube API key (optional - uses mock data if not configured)
- Google Maps API key (optional)

### 🚀 One-Click Setup (Recommended)

**For Windows users:**
```bash
# Run the automated setup script
.\start-dev.bat
```

**For PowerShell users:**
```powershell
# Run the PowerShell setup script
.\start-dev.ps1
```

This will automatically:
- ✅ Check for Node.js installation
- ✅ Install dependencies for both server and client
- ✅ Start the server on `http://localhost:5000`
- ✅ Start the client on `http://localhost:5173`
- ✅ Use dummy data when APIs are not configured

### 🔧 Manual Setup

#### Backend Setup

1. Navigate to server directory:
```bash
cd server
```

2. Install dependencies:
```bash
npm install
```

3. Start development server:
```bash
npm run dev
```

The server will run on `http://localhost:5000`

#### Frontend Setup

1. Navigate to client directory:
```bash
cd client
```

2. Install dependencies:
```bash
npm install
```

3. Start development server:
```bash
npm run dev
```

The client will run on `http://localhost:5173`

### 🎯 What You'll See

- **Server**: Runs on port 5000 with API endpoints
- **Client**: Runs on port 5173 with React frontend
- **Dummy Data**: Automatically used when database/APIs aren't configured
  - Sample cities: Mathura, Agra, Varanasi
  - Sample places and restaurants for each city
  - Mock YouTube vlogs for travel content

## 📡 API Endpoints

### Cities
- `GET /api/cities` - Get all cities
- `GET /api/cities/search?q=query` - Search cities
- `GET /api/cities/:name` - Get specific city
- `GET /api/cities/:name/places` - Get places in a city
- `GET /api/cities/:name/restaurants` - Get restaurants in a city
- `GET /api/cities/:name/culture` - Get culture info for a city

### YouTube Vlogs
- `GET /api/vlogs/:name` - Get vlogs for a city
- `GET /api/vlogs/video/:videoId` - Get specific video details

### Health
- `GET /api/health` - API health check

## 🗄️ Database Setup (Optional)

**Note: The project works perfectly without database setup using dummy data!**

If you want to use a real database:

1. Create MongoDB Atlas account
2. Create a new cluster
3. Get connection string
4. Add to `server/.env` file:
```env
MONGODB_URI=mongodb+srv://username:<EMAIL>/cityunfold?retryWrites=true&w=majority
```

5. Seed the database:
```bash
cd server
npm run seed
```

### 🔄 Fallback Behavior
- **No Database**: Uses sample data from `server/seeds/sampleData.js`
- **Database Error**: Automatically falls back to sample data
- **Database Connected**: Uses real data from MongoDB

## 🔑 API Keys Setup (Optional)

**Note: The project works without API keys using mock data!**

### YouTube Data API (Optional)
If you want real YouTube vlogs instead of mock data:

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Enable YouTube Data API v3
3. Create credentials (API key)
4. Add to `server/.env`:
```env
YOUTUBE_API_KEY=your_youtube_api_key
```

### Google Maps API (Optional)
For future map integration:

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Enable Maps JavaScript API
3. Create credentials (API key)
4. Add to `server/.env`:
```env
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
```

### 🔄 Fallback Behavior
- **No YouTube API**: Uses mock video data with sample titles and thumbnails
- **YouTube API Error**: Falls back to mock data automatically
- **API Configured**: Fetches real YouTube travel vlogs

## 🎯 Features

- ✅ City search and information
- ✅ Top tourist places with details
- ✅ Restaurant recommendations
- ✅ YouTube travel vlogs integration
- ✅ Cultural information
- 🔄 Google Maps integration (coming soon)
- 🔄 Responsive design (coming soon)
- 🔄 User reviews (coming soon)

## 📅 Development Timeline

This project follows a 30-day development plan:
- **Week 1**: Project setup and basic structure ✅
- **Week 2**: Core features (places, restaurants, vlogs)
- **Week 3**: Dynamic UI, filters, culture section
- **Week 4**: Final polish, testing, and deployment

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 🔧 Troubleshooting

### Common Issues

**1. "Node.js is not installed"**
- Download and install Node.js from [nodejs.org](https://nodejs.org/)
- Restart your terminal after installation

**2. "Port 5000 already in use"**
- Change the port in `server/.env`: `PORT=5001`
- Or kill the process using port 5000

**3. "Cannot connect to server"**
- Make sure the server is running on `http://localhost:5000`
- Check if the server terminal shows any errors
- Try restarting the server

**4. "No data showing"**
- This is normal! The app uses dummy data when APIs aren't configured
- Check browser console for any errors
- Ensure both server and client are running

**5. "PowerShell execution policy error"**
- Run: `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`
- Or use the `.bat` file instead: `.\start-dev.bat`

### 🆘 Getting Help

If you encounter issues:
1. Check the browser console (F12) for errors
2. Check the server terminal for error messages
3. Ensure both server (port 5000) and client (port 5173) are running
4. Try restarting both services

## 📄 License

This project is licensed under the MIT License.
