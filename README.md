# CityUnfold 🌍

A comprehensive travel website where users can explore cities and discover top tourist places, restaurants, YouTube vlogs, maps, and cultural information.

## 🚀 Project Structure

```
CityUnfold/
├── client/          # React frontend (Vite)
│   ├── src/
│   ├── public/
│   └── package.json
├── server/          # Express backend
│   ├── controllers/ # API logic
│   ├── models/      # MongoDB schemas
│   ├── routes/      # API routes
│   ├── seeds/       # Sample data
│   └── package.json
└── README.md
```

## 🛠️ Tech Stack

### Frontend
- **React** with Vite
- **CSS/Tailwind** for styling
- **Axios** for API calls

### Backend
- **Node.js** with Express
- **MongoDB** with Mongoose
- **YouTube Data API** for vlogs
- **Google Maps API** for maps

## 🏃‍♂️ Quick Start

### Prerequisites
- Node.js (v16 or higher)
- MongoDB Atlas account (optional for development)
- YouTube API key (optional)
- Google Maps API key (optional)

### Backend Setup

1. Navigate to server directory:
```bash
cd server
```

2. Install dependencies:
```bash
npm install
```

3. Create environment file:
```bash
cp .env.example .env
```

4. Start development server:
```bash
npm run dev
```

The server will run on `http://localhost:5000`

### Frontend Setup

1. Navigate to client directory:
```bash
cd client
```

2. Install dependencies:
```bash
npm install
```

3. Start development server:
```bash
npm run dev
```

The client will run on `http://localhost:5173`

## 📡 API Endpoints

### Cities
- `GET /api/cities` - Get all cities
- `GET /api/cities/search?q=query` - Search cities
- `GET /api/cities/:name` - Get specific city
- `GET /api/cities/:name/places` - Get places in a city
- `GET /api/cities/:name/restaurants` - Get restaurants in a city
- `GET /api/cities/:name/culture` - Get culture info for a city

### YouTube Vlogs
- `GET /api/vlogs/:name` - Get vlogs for a city
- `GET /api/vlogs/video/:videoId` - Get specific video details

### Health
- `GET /api/health` - API health check

## 🗄️ Database Setup

1. Create MongoDB Atlas account
2. Create a new cluster
3. Get connection string
4. Add to `.env` file:
```
MONGODB_URI=mongodb+srv://username:<EMAIL>/cityunfold
```

5. Seed the database:
```bash
npm run seed
```

## 🔑 API Keys Setup

### YouTube Data API
1. Go to Google Cloud Console
2. Enable YouTube Data API v3
3. Create credentials (API key)
4. Add to `.env`:
```
YOUTUBE_API_KEY=your_youtube_api_key
```

### Google Maps API
1. Go to Google Cloud Console
2. Enable Maps JavaScript API
3. Create credentials (API key)
4. Add to `.env`:
```
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
```

## 🎯 Features

- ✅ City search and information
- ✅ Top tourist places with details
- ✅ Restaurant recommendations
- ✅ YouTube travel vlogs integration
- ✅ Cultural information
- 🔄 Google Maps integration (coming soon)
- 🔄 Responsive design (coming soon)
- 🔄 User reviews (coming soon)

## 📅 Development Timeline

This project follows a 30-day development plan:
- **Week 1**: Project setup and basic structure ✅
- **Week 2**: Core features (places, restaurants, vlogs)
- **Week 3**: Dynamic UI, filters, culture section
- **Week 4**: Final polish, testing, and deployment

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
