// Simple API test utility
import { cityService, healthService } from '../services/api';

export const testApiConnection = async () => {
  console.log('🧪 Testing API Connection...');
  
  try {
    // Test health endpoint
    const health = await healthService.checkHealth();
    console.log('✅ Health Check:', health);
    
    // Test cities endpoint
    const cities = await cityService.getAllCities();
    console.log('✅ Cities:', cities);
    
    // Test search
    const searchResults = await cityService.searchCities('Mathura');
    console.log('✅ Search Results:', searchResults);
    
    return true;
  } catch (error) {
    console.error('❌ API Test Failed:', error);
    return false;
  }
};

// Auto-run test in development
if (import.meta.env.DEV) {
  setTimeout(() => {
    testApiConnection();
  }, 1000);
}
