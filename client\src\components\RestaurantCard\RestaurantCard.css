.restaurant-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.restaurant-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.restaurant-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.restaurant-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.restaurant-card:hover .restaurant-image img {
  transform: scale(1.05);
}

.placeholder-image {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.restaurant-icon {
  font-size: 3rem;
  opacity: 0.8;
}

.price-range {
  position: absolute;
  top: 15px;
  right: 15px;
  color: white;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 600;
}

.restaurant-content {
  padding: 20px;
}

.restaurant-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 5px 0;
  line-height: 1.3;
}

.restaurant-type {
  color: #666;
  font-size: 0.9rem;
  margin: 0 0 15px 0;
  font-weight: 500;
}

.restaurant-details {
  margin-bottom: 15px;
}

.restaurant-rating {
  display: flex;
  align-items: center;
  gap: 5px;
}

.rating-stars {
  font-size: 14px;
}

.rating-value {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.cuisine-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 12px;
}

.cuisine-tag {
  background: #f0f0f0;
  color: #666;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.specialties {
  margin-bottom: 12px;
  font-size: 14px;
}

.specialties-label {
  color: #666;
  font-weight: 500;
}

.specialties-list {
  color: #333;
  font-weight: 600;
  margin-left: 5px;
}

.restaurant-timings {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.timings-icon {
  font-size: 16px;
}

.restaurant-address {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.address-icon {
  font-size: 16px;
  margin-top: 1px;
  flex-shrink: 0;
}

.restaurant-contact {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
}

.phone-icon {
  font-size: 16px;
}

/* Responsive design */
@media (max-width: 768px) {
  .restaurant-image {
    height: 180px;
  }
  
  .restaurant-content {
    padding: 15px;
  }
  
  .restaurant-name {
    font-size: 1.1rem;
  }
  
  .restaurant-type {
    font-size: 0.85rem;
  }
  
  .cuisine-tags {
    gap: 4px;
  }
  
  .cuisine-tag {
    padding: 3px 8px;
    font-size: 11px;
  }
}
