import './RestaurantCard.css';

const RestaurantCard = ({ restaurant }) => {
  const getRatingStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push('⭐');
    }
    if (hasHalfStar) {
      stars.push('⭐');
    }
    return stars.join('');
  };

  const getPriceRangeColor = (priceRange) => {
    const colors = {
      '₹': '#4CAF50',
      '₹₹': '#FF9800',
      '₹₹₹': '#F44336',
      '₹₹₹₹': '#9C27B0'
    };
    return colors[priceRange] || '#666';
  };

  return (
    <div className="restaurant-card">
      <div className="restaurant-image">
        {restaurant.image ? (
          <img src={restaurant.image} alt={restaurant.name} />
        ) : (
          <div className="placeholder-image">
            <span className="restaurant-icon">🍽️</span>
          </div>
        )}
        <div className="price-range" style={{ backgroundColor: getPriceRangeColor(restaurant.priceRange) }}>
          {restaurant.priceRange || '₹₹'}
        </div>
      </div>
      
      <div className="restaurant-content">
        <h3 className="restaurant-name">{restaurant.name}</h3>
        <p className="restaurant-type">{restaurant.type}</p>
        
        <div className="restaurant-details">
          {restaurant.rating > 0 && (
            <div className="restaurant-rating">
              <span className="rating-stars">{getRatingStars(restaurant.rating)}</span>
              <span className="rating-value">{restaurant.rating}</span>
            </div>
          )}
        </div>
        
        {restaurant.cuisine && restaurant.cuisine.length > 0 && (
          <div className="cuisine-tags">
            {restaurant.cuisine.slice(0, 3).map((cuisine, index) => (
              <span key={index} className="cuisine-tag">{cuisine}</span>
            ))}
          </div>
        )}
        
        {restaurant.specialties && restaurant.specialties.length > 0 && (
          <div className="specialties">
            <span className="specialties-label">Must Try:</span>
            <span className="specialties-list">
              {restaurant.specialties.slice(0, 2).join(', ')}
            </span>
          </div>
        )}
        
        {restaurant.timings && (
          <div className="restaurant-timings">
            <span className="timings-icon">🕒</span>
            <span>{restaurant.timings.open} - {restaurant.timings.close}</span>
          </div>
        )}
        
        {restaurant.address && (
          <div className="restaurant-address">
            <span className="address-icon">📍</span>
            <span>{restaurant.address}</span>
          </div>
        )}
        
        {restaurant.contact?.phone && (
          <div className="restaurant-contact">
            <span className="phone-icon">📞</span>
            <span>{restaurant.contact.phone}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default RestaurantCard;
