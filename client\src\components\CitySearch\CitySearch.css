.city-search {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.search-container {
  position: relative;
  margin-bottom: 30px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: 15px 50px 15px 20px;
  font-size: 16px;
  border: 2px solid #e0e0e0;
  border-radius: 25px;
  outline: none;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-input:focus {
  border-color: #4CAF50;
  box-shadow: 0 4px 20px rgba(76, 175, 80, 0.2);
}

.search-icon {
  position: absolute;
  right: 20px;
  font-size: 18px;
  color: #666;
}

.search-spinner-container {
  position: absolute;
  right: 50px;
  display: flex;
  align-items: center;
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
}

.search-result-item {
  padding: 15px 20px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.search-result-item:hover {
  background-color: #f8f9fa;
}

.search-result-item:last-child {
  border-bottom: none;
}

.city-name {
  font-weight: 600;
  font-size: 16px;
  color: #333;
  margin-bottom: 4px;
}

.city-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 2px;
}

.city-state {
  font-size: 12px;
  color: #999;
  font-style: italic;
}

.popular-cities {
  text-align: center;
}

.popular-cities h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 24px;
}

.popular-cities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.popular-city-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 25px 20px;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.popular-city-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.popular-city-card .city-name {
  color: white;
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 8px;
}

.popular-city-card .city-description {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  line-height: 1.4;
}

/* Responsive design */
@media (max-width: 768px) {
  .city-search {
    padding: 15px;
  }
  
  .search-input {
    padding: 12px 45px 12px 15px;
    font-size: 14px;
  }
  
  .popular-cities-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .popular-city-card {
    padding: 20px 15px;
  }
  
  .popular-cities h3 {
    font-size: 20px;
  }
}
