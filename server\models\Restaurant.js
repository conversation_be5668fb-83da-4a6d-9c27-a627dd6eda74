import mongoose from 'mongoose';

const restaurantSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  city: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  rating: {
    type: Number,
    min: 0,
    max: 5,
    default: 0
  },
  type: {
    type: String,
    required: true,
    trim: true
  },
  cuisine: {
    type: [String],
    default: []
  },
  priceRange: {
    type: String,
    enum: ['₹', '₹₹', '₹₹₹', '₹₹₹₹'],
    default: '₹₹'
  },
  address: {
    type: String,
    trim: true
  },
  location: {
    coordinates: {
      latitude: Number,
      longitude: Number
    }
  },
  contact: {
    phone: String,
    email: String
  },
  timings: {
    open: String,
    close: String,
    note: String
  },
  specialties: {
    type: [String],
    default: []
  },
  image: {
    type: String,
    trim: true
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Create indexes for efficient querying
restaurantSchema.index({ city: 1, type: 1 });
restaurantSchema.index({ city: 1, rating: -1 });
restaurantSchema.index({ name: 'text', type: 'text', cuisine: 'text' });

const Restaurant = mongoose.model('Restaurant', restaurantSchema);

export default Restaurant;
