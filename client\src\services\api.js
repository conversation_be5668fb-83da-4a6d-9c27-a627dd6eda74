import axios from 'axios';

// Base API configuration
const API_BASE_URL = 'http://localhost:5000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('❌ API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    console.log(`✅ API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('❌ API Response Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// API service functions
export const cityService = {
  // Get all cities
  getAllCities: async () => {
    const response = await api.get('/cities');
    return response.data;
  },

  // Search cities
  searchCities: async (query) => {
    const response = await api.get(`/cities/search?q=${encodeURIComponent(query)}`);
    return response.data;
  },

  // Get specific city
  getCityByName: async (name) => {
    const response = await api.get(`/cities/${encodeURIComponent(name)}`);
    return response.data;
  },

  // Get places in a city
  getCityPlaces: async (name, options = {}) => {
    const { category, limit = 10 } = options;
    let url = `/cities/${encodeURIComponent(name)}/places?limit=${limit}`;
    if (category) {
      url += `&category=${encodeURIComponent(category)}`;
    }
    const response = await api.get(url);
    return response.data;
  },

  // Get restaurants in a city
  getCityRestaurants: async (name, options = {}) => {
    const { type, priceRange, limit = 10 } = options;
    let url = `/cities/${encodeURIComponent(name)}/restaurants?limit=${limit}`;
    if (type) {
      url += `&type=${encodeURIComponent(type)}`;
    }
    if (priceRange) {
      url += `&priceRange=${encodeURIComponent(priceRange)}`;
    }
    const response = await api.get(url);
    return response.data;
  },

  // Get culture information
  getCityCulture: async (name) => {
    const response = await api.get(`/cities/${encodeURIComponent(name)}/culture`);
    return response.data;
  },
};

export const vlogService = {
  // Get vlogs for a city
  getCityVlogs: async (name, limit = 3) => {
    const response = await api.get(`/vlogs/${encodeURIComponent(name)}?limit=${limit}`);
    return response.data;
  },

  // Get specific video details
  getVideoDetails: async (videoId) => {
    const response = await api.get(`/vlogs/video/${videoId}`);
    return response.data;
  },
};

export const healthService = {
  // Check API health
  checkHealth: async () => {
    const response = await api.get('/health');
    return response.data;
  },
};

export default api;
