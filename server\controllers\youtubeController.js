// YouTube API controller for fetching travel vlogs
import axios from 'axios';

export const getCityVlogs = async (req, res) => {
  try {
    const { name } = req.params;
    const { limit = 3 } = req.query;

    // Check if YouTube API key is available
    if (!process.env.YOUTUBE_API_KEY) {
      // Return mock data if API key is not available
      const mockVlogs = [
        {
          id: 'mock1',
          title: `${name} Travel Guide - Best Places to Visit`,
          description: `Complete travel guide for ${name} covering top attractions, food, and culture`,
          thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
          videoId: 'dQw4w9WgXcQ',
          channelTitle: 'Travel Explorer',
          publishedAt: new Date().toISOString(),
          viewCount: '125000',
          duration: 'PT12M34S'
        },
        {
          id: 'mock2',
          title: `${name} Food Tour - Must Try Local Dishes`,
          description: `Exploring the best local food and restaurants in ${name}`,
          thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
          videoId: 'dQw4w9WgXcQ',
          channelTitle: 'Foodie Adventures',
          publishedAt: new Date().toISOString(),
          viewCount: '89000',
          duration: 'PT8M15S'
        },
        {
          id: 'mock3',
          title: `${name} Cultural Experience - Festivals and Traditions`,
          description: `Experience the rich culture and traditions of ${name}`,
          thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
          videoId: 'dQw4w9WgXcQ',
          channelTitle: 'Culture Connect',
          publishedAt: new Date().toISOString(),
          viewCount: '67000',
          duration: 'PT15M22S'
        }
      ];

      return res.json({
        success: true,
        city: name,
        count: mockVlogs.length,
        data: mockVlogs.slice(0, parseInt(limit)),
        note: 'Mock data - YouTube API key not configured'
      });
    }

    // YouTube API implementation
    const searchQuery = `${name} travel vlog guide`;
    const apiUrl = `https://www.googleapis.com/youtube/v3/search?part=snippet&q=${encodeURIComponent(searchQuery)}&type=video&maxResults=${limit}&key=${process.env.YOUTUBE_API_KEY}`;

    const response = await axios.get(apiUrl);
    const data = response.data;

    if (response.status !== 200) {
      throw new Error(data.error?.message || 'YouTube API error');
    }

    // Format the response
    const vlogs = data.items.map(item => ({
      id: item.id.videoId,
      title: item.snippet.title,
      description: item.snippet.description,
      thumbnail: item.snippet.thumbnails.high?.url || item.snippet.thumbnails.default.url,
      videoId: item.id.videoId,
      channelTitle: item.snippet.channelTitle,
      publishedAt: item.snippet.publishedAt
    }));

    res.json({
      success: true,
      city: name,
      count: vlogs.length,
      data: vlogs
    });

  } catch (error) {
    console.error('Error fetching YouTube vlogs:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching YouTube vlogs',
      error: error.message
    });
  }
};

export const getVlogDetails = async (req, res) => {
  try {
    const { videoId } = req.params;

    if (!process.env.YOUTUBE_API_KEY) {
      return res.status(503).json({
        success: false,
        message: 'YouTube API key not configured'
      });
    }

    const apiUrl = `https://www.googleapis.com/youtube/v3/videos?part=snippet,statistics,contentDetails&id=${videoId}&key=${process.env.YOUTUBE_API_KEY}`;

    const response = await axios.get(apiUrl);
    const data = response.data;

    if (response.status !== 200) {
      throw new Error(data.error?.message || 'YouTube API error');
    }

    if (data.items.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Video not found'
      });
    }

    const video = data.items[0];
    const videoDetails = {
      id: video.id,
      title: video.snippet.title,
      description: video.snippet.description,
      thumbnail: video.snippet.thumbnails.high?.url || video.snippet.thumbnails.default.url,
      channelTitle: video.snippet.channelTitle,
      publishedAt: video.snippet.publishedAt,
      viewCount: video.statistics.viewCount,
      likeCount: video.statistics.likeCount,
      duration: video.contentDetails.duration
    };

    res.json({
      success: true,
      data: videoDetails
    });

  } catch (error) {
    console.error('Error fetching video details:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching video details',
      error: error.message
    });
  }
};
