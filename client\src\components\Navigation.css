.main-navigation {
  background: white;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 70px;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
  cursor: pointer;
}

.brand-icon {
  font-size: 2rem;
}

.brand-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-links {
  display: flex;
  gap: 10px;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  background: transparent;
  color: #666;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  border-radius: 25px;
  transition: all 0.3s ease;
  text-decoration: none;
}

.nav-link:hover {
  background: #f8f9fa;
  color: #333;
  transform: translateY(-1px);
}

.nav-link.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.nav-icon {
  font-size: 1.1rem;
}

.nav-label {
  font-weight: 600;
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  width: 32px;
  height: 32px;
}

.mobile-menu-toggle span {
  width: 100%;
  height: 3px;
  background: #333;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.mobile-menu-toggle.open span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.open span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.open span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

.mobile-nav {
  display: none;
  background: white;
  border-top: 1px solid #e9ecef;
  padding: 20px;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.mobile-nav.open {
  max-height: 300px;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 15px 20px;
  border: none;
  background: transparent;
  color: #666;
  font-size: 1.1rem;
  font-weight: 500;
  cursor: pointer;
  border-radius: 15px;
  transition: all 0.3s ease;
  margin-bottom: 8px;
  text-align: left;
}

.mobile-nav-link:hover {
  background: #f8f9fa;
  color: #333;
}

.mobile-nav-link.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.mobile-nav-link:last-child {
  margin-bottom: 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }
  
  .mobile-menu-toggle {
    display: flex;
  }
  
  .mobile-nav {
    display: block;
  }
  
  .nav-container {
    padding: 0 15px;
    height: 60px;
  }
  
  .nav-brand {
    font-size: 1.3rem;
  }
  
  .brand-icon {
    font-size: 1.8rem;
  }
}

@media (max-width: 480px) {
  .nav-container {
    padding: 0 10px;
  }
  
  .nav-brand {
    font-size: 1.2rem;
  }
  
  .brand-icon {
    font-size: 1.6rem;
  }
  
  .mobile-nav {
    padding: 15px;
  }
  
  .mobile-nav-link {
    padding: 12px 15px;
    font-size: 1rem;
  }
}

/* Animation for smooth transitions */
@media (prefers-reduced-motion: no-preference) {
  .nav-link,
  .mobile-nav-link {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .mobile-nav {
    transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}
