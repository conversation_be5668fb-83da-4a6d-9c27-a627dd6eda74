import { useState } from 'react';
import './ResponsiveNav.css';

const ResponsiveNav = ({ 
  tabs, 
  activeTab, 
  onTabChange, 
  showFilters = false, 
  onToggleFilters 
}) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const handleTabClick = (tabId) => {
    onTabChange(tabId);
    setIsMenuOpen(false); // Close mobile menu after selection
  };

  return (
    <nav className="responsive-nav">
      {/* Desktop Navigation */}
      <div className="nav-desktop">
        <div className="nav-tabs">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              className={`nav-tab ${activeTab === tab.id ? 'active' : ''}`}
              onClick={() => handleTabClick(tab.id)}
            >
              <span className="tab-label">{tab.label}</span>
              {tab.count > 0 && (
                <span className="tab-count">({tab.count})</span>
              )}
            </button>
          ))}
        </div>
        
        {showFilters && (
          <button 
            className="filter-toggle-btn"
            onClick={onToggleFilters}
            title="Toggle filters"
          >
            🔍 Filters
          </button>
        )}
      </div>

      {/* Mobile Navigation */}
      <div className="nav-mobile">
        <div className="nav-header">
          <div className="current-tab">
            {tabs.find(tab => tab.id === activeTab)?.label || 'Select Tab'}
          </div>
          
          <div className="nav-actions">
            {showFilters && (
              <button 
                className="filter-toggle-btn mobile"
                onClick={onToggleFilters}
                title="Toggle filters"
              >
                🔍
              </button>
            )}
            <button 
              className={`menu-toggle ${isMenuOpen ? 'open' : ''}`}
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-label="Toggle menu"
            >
              <span></span>
              <span></span>
              <span></span>
            </button>
          </div>
        </div>

        {/* Mobile Menu Dropdown */}
        <div className={`nav-dropdown ${isMenuOpen ? 'open' : ''}`}>
          {tabs.map((tab) => (
            <button
              key={tab.id}
              className={`nav-tab-mobile ${activeTab === tab.id ? 'active' : ''}`}
              onClick={() => handleTabClick(tab.id)}
            >
              <span className="tab-label">{tab.label}</span>
              {tab.count > 0 && (
                <span className="tab-count">({tab.count})</span>
              )}
            </button>
          ))}
        </div>
      </div>
    </nav>
  );
};

export default ResponsiveNav;
