import { useState } from 'react'
import './App.css'
import CitySearch from './components/CitySearch/CitySearch'
import CityInfo from './components/cityInfo/CityInfo'
import LoadingSpinner from './components/LoadingSpinner/LoadingSpinner'
import Navigation from './components/Navigation/Navigation'
import About from './pages/About'
import Contact from './pages/Contact'
import './utils/testApi' // Auto-run API tests in development

function App() {
  const [selectedCity, setSelectedCity] = useState(null)
  const [loading, setLoading] = useState(false)
  const [currentPage, setCurrentPage] = useState('home')

  const renderPage = () => {
    switch (currentPage) {
      case 'about':
        return <About />;
      case 'contact':
        return <Contact />;
      case 'home':
      default:
        return (
          <div className="home-page">
            <header className="app-header">
              <h1>🌍 CityUnfold</h1>
              <p>Discover amazing places, food, culture & vlogs</p>
            </header>

            <main className="main-content">
              <CitySearch
                onCitySelect={setSelectedCity}
                setLoading={setLoading}
              />

              {loading && (
                <LoadingSpinner
                  size="large"
                  message="Loading city information..."
                  fullScreen={false}
                />
              )}

              {selectedCity && !loading && (
                <CityInfo city={selectedCity} />
              )}
            </main>

            <footer className="app-footer">
              <p>&copy; 2025 CityUnfold. Explore the world, one city at a time.</p>
            </footer>
          </div>
        );
    }
  };

  return (
    <div className="App">
      <Navigation
        currentPage={currentPage}
        onPageChange={setCurrentPage}
      />
      {renderPage()}
    </div>
  )
}

export default App