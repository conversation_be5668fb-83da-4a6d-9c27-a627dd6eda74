.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 400px;
}

.toast {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  border-left: 4px solid;
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s ease;
  max-width: 100%;
  overflow: hidden;
}

.toast-show {
  transform: translateX(0);
  opacity: 1;
}

.toast-content {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  gap: 12px;
}

.toast-icon {
  font-size: 18px;
  flex-shrink: 0;
}

.toast-message {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
  color: #333;
}

.toast-close {
  background: none;
  border: none;
  font-size: 20px;
  color: #999;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.toast-close:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #666;
}

/* Toast Types */
.toast-success {
  border-left-color: #4CAF50;
  background: linear-gradient(135deg, #f8fff8 0%, #f0fff0 100%);
}

.toast-error {
  border-left-color: #f44336;
  background: linear-gradient(135deg, #fff8f8 0%, #fff0f0 100%);
}

.toast-warning {
  border-left-color: #ff9800;
  background: linear-gradient(135deg, #fffbf0 0%, #fff8e1 100%);
}

.toast-info {
  border-left-color: #2196f3;
  background: linear-gradient(135deg, #f0f8ff 0%, #e3f2fd 100%);
}

/* Animations */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .toast-container {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
  
  .toast {
    transform: translateY(-100%);
  }
  
  .toast-show {
    transform: translateY(0);
  }
  
  .toast-content {
    padding: 14px 16px;
    gap: 10px;
  }
  
  .toast-message {
    font-size: 13px;
  }
  
  .toast-icon {
    font-size: 16px;
  }
  
  .toast-close {
    width: 20px;
    height: 20px;
    font-size: 18px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .toast {
    background: #2d3748;
    color: white;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }
  
  .toast-message {
    color: #e2e8f0;
  }
  
  .toast-close {
    color: #a0aec0;
  }
  
  .toast-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #e2e8f0;
  }
  
  .toast-success {
    background: linear-gradient(135deg, #1a2e1a 0%, #2d4a2d 100%);
  }
  
  .toast-error {
    background: linear-gradient(135deg, #2e1a1a 0%, #4a2d2d 100%);
  }
  
  .toast-warning {
    background: linear-gradient(135deg, #2e2a1a 0%, #4a452d 100%);
  }
  
  .toast-info {
    background: linear-gradient(135deg, #1a252e 0%, #2d3f4a 100%);
  }
}
