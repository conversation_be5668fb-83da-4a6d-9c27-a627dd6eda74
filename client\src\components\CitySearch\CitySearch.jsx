import { useState, useEffect } from 'react';
import { cityService, vlogService } from '../../services/api';
import LoadingSpinner from '../LoadingSpinner/LoadingSpinner';
import { useToast, ToastContainer } from '../Toast/Toast';
import './CitySearch.css';

const CitySearch = ({ onCitySelect, setLoading }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [popularCities, setPopularCities] = useState([]);
  const { toasts, showError, showSuccess, removeToast } = useToast();

  // Load popular cities on component mount
  useEffect(() => {
    loadPopularCities();
  }, []);

  const loadPopularCities = async () => {
    try {
      const response = await cityService.getAllCities();
      if (response.success) {
        setPopularCities(response.data.slice(0, 6)); // Show top 6 cities
      }
    } catch (error) {
      console.error('Error loading popular cities:', error);
      showError('Failed to load popular cities. Using sample data.');
      // Set mock data if API fails
      setPopularCities([
        { name: 'Mathura', description: 'Holy city of Lord Krishna' },
        { name: 'Agra', description: 'Home to the Taj Mahal' },
        { name: 'Varanasi', description: 'Spiritual capital of India' },
      ]);
    }
  };

  const handleSearch = async (query) => {
    if (!query.trim()) {
      setSearchResults([]);
      setShowResults(false);
      return;
    }

    setIsSearching(true);
    try {
      const response = await cityService.searchCities(query);
      if (response.success) {
        setSearchResults(response.data);
        setShowResults(true);
      }
    } catch (error) {
      console.error('Error searching cities:', error);
      showError('Search failed. Please try again.');
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  const handleInputChange = (e) => {
    const value = e.target.value;
    setSearchQuery(value);
    
    // Debounce search
    clearTimeout(window.searchTimeout);
    window.searchTimeout = setTimeout(() => {
      handleSearch(value);
    }, 300);
  };

  const handleCityClick = async (cityName) => {
    setLoading(true);
    setShowResults(false);
    setSearchQuery(cityName);

    try {
      // Fetch comprehensive city data
      const [cityData, places, restaurants, culture, vlogs] = await Promise.all([
        cityService.getCityByName(cityName),
        cityService.getCityPlaces(cityName),
        cityService.getCityRestaurants(cityName),
        cityService.getCityCulture(cityName),
        vlogService.getCityVlogs(cityName)
      ]);

      const cityInfo = {
        ...cityData.data,
        places: places.data || [],
        restaurants: restaurants.data || [],
        culture: culture.data || {},
        vlogs: vlogs.data || []
      };

      onCitySelect(cityInfo);
      showSuccess(`Loaded information for ${cityName}`);
    } catch (error) {
      console.error('Error fetching city data:', error);
      showError(`Failed to load data for ${cityName}. Showing sample information.`);
      // Create mock data for demonstration
      const mockCityInfo = {
        name: cityName,
        description: `Explore the beautiful city of ${cityName}`,
        places: [],
        restaurants: [],
        culture: { culture: 'Rich cultural heritage', description: 'A city with amazing history' },
        vlogs: []
      };
      onCitySelect(mockCityInfo);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && searchResults.length > 0) {
      handleCityClick(searchResults[0].name);
    }
  };

  return (
    <>
      <ToastContainer toasts={toasts} onRemoveToast={removeToast} />
      <div className="city-search">
      <div className="search-container">
        <div className="search-input-wrapper">
          <input
            type="text"
            placeholder="Search for a city (e.g., Mathura, Agra, Varanasi)..."
            value={searchQuery}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            className="search-input"
          />
          <div className="search-icon">🔍</div>
          {isSearching && (
            <div className="search-spinner-container">
              <LoadingSpinner size="small" color="primary" />
            </div>
          )}
        </div>

        {showResults && searchResults.length > 0 && (
          <div className="search-results">
            {searchResults.map((city, index) => (
              <div
                key={index}
                className="search-result-item"
                onClick={() => handleCityClick(city.name)}
              >
                <div className="city-name">{city.name}</div>
                <div className="city-description">{city.description}</div>
                {city.state && <div className="city-state">{city.state}</div>}
              </div>
            ))}
          </div>
        )}
      </div>

      {!showResults && popularCities.length > 0 && (
        <div className="popular-cities">
          <h3>🔥 Popular Destinations</h3>
          <div className="popular-cities-grid">
            {popularCities.map((city, index) => (
              <div
                key={index}
                className="popular-city-card"
                onClick={() => handleCityClick(city.name)}
              >
                <div className="city-name">{city.name}</div>
                <div className="city-description">{city.description}</div>
              </div>
            ))}
          </div>
        </div>
      )}
      </div>
    </>
  );
};

export default CitySearch;
