import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { City, Place, Restaurant } from '../models/index.js';
import { sampleCities, samplePlaces, sampleRestaurants } from './sampleData.js';

// Load environment variables
dotenv.config();

const seedDatabase = async () => {
  try {
    // Connect to MongoDB
    if (!process.env.MONGODB_URI) {
      console.log('❌ MongoDB URI not found in environment variables');
      console.log('   Please set MONGODB_URI in your .env file');
      process.exit(1);
    }

    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Clear existing data
    console.log('🧹 Clearing existing data...');
    await City.deleteMany({});
    await Place.deleteMany({});
    await Restaurant.deleteMany({});
    console.log('✅ Existing data cleared');

    // Seed cities
    console.log('🏙️  Seeding cities...');
    const cities = await City.insertMany(sampleCities);
    console.log(`✅ ${cities.length} cities added`);

    // Seed places
    console.log('📍 Seeding places...');
    const places = await Place.insertMany(samplePlaces);
    console.log(`✅ ${places.length} places added`);

    // Seed restaurants
    console.log('🍽️  Seeding restaurants...');
    const restaurants = await Restaurant.insertMany(sampleRestaurants);
    console.log(`✅ ${restaurants.length} restaurants added`);

    console.log('\n🎉 Database seeded successfully!');
    console.log('\n📊 Summary:');
    console.log(`   Cities: ${cities.length}`);
    console.log(`   Places: ${places.length}`);
    console.log(`   Restaurants: ${restaurants.length}`);

  } catch (error) {
    console.error('❌ Error seeding database:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('✅ Database connection closed');
    process.exit(0);
  }
};

// Run the seeder
seedDatabase();
