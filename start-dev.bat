@echo off
echo.
echo 🚀 Starting CityUnfold Development Environment...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js is installed

REM Install server dependencies if needed
if not exist "server\node_modules" (
    echo 📦 Installing server dependencies...
    cd server
    npm install
    cd ..
    echo ✅ Server dependencies installed
) else (
    echo ✅ Server dependencies already installed
)

REM Install client dependencies if needed
if not exist "client\node_modules" (
    echo 📦 Installing client dependencies...
    cd client
    npm install
    cd ..
    echo ✅ Client dependencies installed
) else (
    echo ✅ Client dependencies already installed
)

REM Check if axios is installed in client
if not exist "client\node_modules\axios" (
    echo 📦 Installing missing axios dependency...
    cd client
    npm install axios
    cd ..
    echo ✅ Axios installed
)

echo.
echo 🎯 Starting development servers...
echo    📍 Server will run on: http://localhost:5000
echo    📍 Client will run on: http://localhost:5173
echo.
echo 💡 Note: This project uses dummy data when APIs are not configured
echo    - MongoDB: Uses sample cities, places, and restaurants
echo    - YouTube API: Uses mock video data
echo.
echo 🔧 To configure real APIs, edit server\.env file
echo.

REM Start server in new window
echo 🚀 Starting server...
start "CityUnfold Server" cmd /k "cd server && npm run dev"

REM Wait a moment for server to start
timeout /t 3 /nobreak >nul

REM Start client in current window
echo 🚀 Starting client...
cd client
npm run dev
