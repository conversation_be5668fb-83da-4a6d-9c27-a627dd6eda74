import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import mongoose from 'mongoose';
import apiRoutes from './routes/index.js';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Basic route for testing
app.get('/', (req, res) => {
  res.json({ 
    message: 'Welcome to CityUnfold API!', 
    status: 'Server is running successfully',
    timestamp: new Date().toISOString()
  });
});

// API Routes
app.use('/api', apiRoutes);

// Test API endpoint (legacy)
app.get('/api/hello', (req, res) => {
  res.json({
    message: 'Hello from CityUnfold API!',
    version: '1.0.0',
    endpoints: {
      cities: '/api/cities',
      places: '/api/cities/:name/places',
      restaurants: '/api/cities/:name/restaurants',
      culture: '/api/cities/:name/culture',
      vlogs: '/api/vlogs/:name',
      health: '/api/health'
    }
  });
});

// MongoDB connection
const connectDB = async () => {
  try {
    if (process.env.MONGODB_URI) {
      await mongoose.connect(process.env.MONGODB_URI);
      console.log('✅ MongoDB connected successfully');
    } else {
      console.log('⚠️  MongoDB URI not found in environment variables');
      console.log('   Server will run without database connection');
    }
  } catch (error) {
    console.error('❌ MongoDB connection error:', error.message);
    console.log('   Server will continue without database connection');
  }
};

// Connect to database
connectDB();

// Start server
app.listen(PORT, () => {
  console.log(`🚀 CityUnfold Server running on port ${PORT}`);
  console.log(`📍 Local: http://localhost:${PORT}`);
  console.log(`🧪 Test endpoint: http://localhost:${PORT}/api/hello`);
});

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Shutting down server...');
  await mongoose.connection.close();
  console.log('✅ Database connection closed');
  process.exit(0);
});
