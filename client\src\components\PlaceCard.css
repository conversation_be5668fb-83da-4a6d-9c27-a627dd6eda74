.place-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.place-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.place-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.place-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.place-card:hover .place-image img {
  transform: scale(1.05);
}

.placeholder-image {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-icon {
  font-size: 3rem;
  opacity: 0.8;
}

.place-category {
  position: absolute;
  top: 15px;
  left: 15px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

.place-content {
  padding: 20px;
}

.place-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 10px 0;
  line-height: 1.3;
}

.place-description {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0 0 15px 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.place-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  flex-wrap: wrap;
  gap: 10px;
}

.place-rating {
  display: flex;
  align-items: center;
  gap: 5px;
}

.rating-stars {
  font-size: 14px;
}

.rating-value {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.entry-fee {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
}

.fee-label {
  color: #666;
}

.fee-value {
  font-weight: 600;
  color: #4CAF50;
}

.visiting-hours {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.hours-icon {
  font-size: 16px;
}

.hours-note {
  font-size: 12px;
  color: #999;
  font-style: italic;
  margin-top: 2px;
}

.place-address {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.address-icon {
  font-size: 16px;
  margin-top: 1px;
  flex-shrink: 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .place-image {
    height: 180px;
  }
  
  .place-content {
    padding: 15px;
  }
  
  .place-name {
    font-size: 1.1rem;
  }
  
  .place-description {
    font-size: 0.85rem;
  }
  
  .place-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
