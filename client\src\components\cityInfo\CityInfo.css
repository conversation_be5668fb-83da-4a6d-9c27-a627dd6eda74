.city-info {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.city-header {
  display: flex;
  align-items: center;
  gap: 30px;
  margin-bottom: 30px;
  padding: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  color: white;
}

.city-title h1 {
  font-size: 2.5rem;
  margin: 0 0 10px 0;
  font-weight: 700;
}

.city-description {
  font-size: 1.1rem;
  margin: 0;
  opacity: 0.9;
  line-height: 1.5;
}

.city-image {
  flex-shrink: 0;
}

.city-image img {
  width: 150px;
  height: 150px;
  border-radius: 15px;
  object-fit: cover;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.city-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
  border-bottom: 2px solid #f0f0f0;
  overflow-x: auto;
  padding-bottom: 10px;
}

.tab-button {
  padding: 12px 20px;
  border: none;
  background: transparent;
  color: #666;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 25px;
  transition: all 0.3s ease;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 5px;
}

.tab-button:hover {
  background-color: #f8f9fa;
  color: #333;
}

.tab-button.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.tab-count {
  font-size: 12px;
  opacity: 0.8;
}

.tab-content {
  min-height: 400px;
}

.places-grid,
.restaurants-grid,
.vlogs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.culture-section {
  max-width: 800px;
  margin: 0 auto;
}

.culture-content {
  display: grid;
  gap: 30px;
}

.culture-item {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-left: 5px solid #667eea;
}

.culture-item h3 {
  color: #333;
  margin: 0 0 15px 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.culture-item p {
  color: #666;
  line-height: 1.6;
  margin: 0;
  font-size: 1rem;
}

.no-data {
  text-align: center;
  padding: 60px 20px;
  color: #666;
  background: #f8f9fa;
  border-radius: 15px;
  border: 2px dashed #ddd;
}

.no-data p:first-child {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 10px;
}

.no-data p:last-child {
  font-size: 0.9rem;
  opacity: 0.8;
}

/* Responsive design */
@media (max-width: 768px) {
  .city-info {
    padding: 15px;
  }
  
  .city-header {
    flex-direction: column;
    text-align: center;
    gap: 20px;
    padding: 25px 20px;
  }
  
  .city-title h1 {
    font-size: 2rem;
  }
  
  .city-image img {
    width: 120px;
    height: 120px;
  }
  
  .city-tabs {
    gap: 5px;
  }
  
  .tab-button {
    padding: 10px 15px;
    font-size: 14px;
  }
  
  .places-grid,
  .restaurants-grid,
  .vlogs-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .culture-item {
    padding: 20px;
  }
}
