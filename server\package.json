{"name": "cityunfold-server", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "seed": "node seeds/seedDatabase.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["travel", "city", "tourism", "api"], "author": "", "license": "ISC", "description": "Backend API for CityUnfold travel application", "dependencies": {"axios": "^1.11.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "mongoose": "^8.16.5"}, "devDependencies": {"nodemon": "^3.1.10"}}