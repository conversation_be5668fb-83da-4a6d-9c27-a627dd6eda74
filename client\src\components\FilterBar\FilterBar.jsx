import { useState } from 'react';
import './FilterBar.css';

const FilterBar = ({ activeTab, onFilterChange, filters = {} }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const filterOptions = {
    places: {
      category: ['All', 'Temple', 'Monument', 'Museum', 'Park', 'Market', 'Heritage', 'Nature'],
      rating: ['All', '4+ Stars', '3+ Stars', '2+ Stars'],
      entryFee: ['All', 'Free', 'Paid']
    },
    restaurants: {
      cuisine: ['All', 'North Indian', 'South Indian', 'Chinese', 'Continental', 'Street Food', 'Sweets'],
      priceRange: ['All', '₹', '₹₹', '₹₹₹', '₹₹₹₹'],
      rating: ['All', '4+ Stars', '3+ Stars', '2+ Stars']
    },
    vlogs: {
      duration: ['All', 'Short (< 5 min)', 'Medium (5-15 min)', 'Long (> 15 min)'],
      viewCount: ['All', 'Popular (100K+ views)', 'Trending (10K+ views)', 'Recent'],
      language: ['All', 'Hindi', 'English', 'Regional']
    }
  };

  const currentFilters = filterOptions[activeTab] || {};

  const handleFilterChange = (filterType, value) => {
    const newFilters = {
      ...filters,
      [filterType]: value === 'All' ? null : value
    };
    onFilterChange(newFilters);
  };

  const clearAllFilters = () => {
    onFilterChange({});
  };

  const hasActiveFilters = Object.values(filters).some(value => value !== null && value !== undefined);

  if (!currentFilters || Object.keys(currentFilters).length === 0) {
    return null;
  }

  return (
    <div className="filter-bar">
      <div className="filter-header">
        <div className="filter-title">
          <span className="filter-icon">🔍</span>
          <span>Filters</span>
          {hasActiveFilters && <span className="active-indicator">•</span>}
        </div>
        
        <div className="filter-actions">
          {hasActiveFilters && (
            <button 
              onClick={clearAllFilters}
              className="clear-filters-btn"
              title="Clear all filters"
            >
              Clear All
            </button>
          )}
          <button 
            onClick={() => setIsExpanded(!isExpanded)}
            className={`expand-btn ${isExpanded ? 'expanded' : ''}`}
            title={isExpanded ? 'Collapse filters' : 'Expand filters'}
          >
            {isExpanded ? '▲' : '▼'}
          </button>
        </div>
      </div>

      <div className={`filter-content ${isExpanded ? 'expanded' : ''}`}>
        {Object.entries(currentFilters).map(([filterType, options]) => (
          <div key={filterType} className="filter-group">
            <label className="filter-label">
              {filterType.charAt(0).toUpperCase() + filterType.slice(1)}:
            </label>
            <select
              value={filters[filterType] || 'All'}
              onChange={(e) => handleFilterChange(filterType, e.target.value)}
              className="filter-select"
            >
              {options.map((option) => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
          </div>
        ))}
      </div>

      {/* Quick filter chips for mobile */}
      <div className="quick-filters">
        {Object.entries(filters).map(([filterType, value]) => {
          if (!value) return null;
          return (
            <div key={filterType} className="filter-chip">
              <span>{filterType}: {value}</span>
              <button 
                onClick={() => handleFilterChange(filterType, 'All')}
                className="remove-filter"
                title={`Remove ${filterType} filter`}
              >
                ×
              </button>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default FilterBar;
