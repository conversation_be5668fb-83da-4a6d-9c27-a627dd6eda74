import mongoose from 'mongoose';

const placeSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  image: {
    type: String,
    required: true,
    trim: true
  },
  city: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  location: {
    address: {
      type: String,
      trim: true
    },
    coordinates: {
      latitude: {
        type: Number,
        required: true
      },
      longitude: {
        type: Number,
        required: true
      }
    }
  },
  category: {
    type: String,
    enum: ['temple', 'monument', 'museum', 'park', 'market', 'heritage', 'nature', 'other'],
    default: 'other'
  },
  rating: {
    type: Number,
    min: 0,
    max: 5,
    default: 0
  },
  visitingHours: {
    open: String,
    close: String,
    note: String
  },
  entryFee: {
    type: String,
    default: 'Free'
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Create indexes for efficient querying
placeSchema.index({ city: 1, category: 1 });
placeSchema.index({ name: 'text', description: 'text' });

const Place = mongoose.model('Place', placeSchema);

export default Place;
