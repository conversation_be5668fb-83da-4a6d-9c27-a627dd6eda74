import { useState, useMemo } from 'react';
import PlaceCard from './PlaceCard';
import RestaurantCard from './RestaurantCard';
import VlogCard from './VlogCard';
import FilterBar from './FilterBar';
import ResponsiveNav from './ResponsiveNav';
import './CityInfo.css';

const CityInfo = ({ city }) => {
  const [activeTab, setActiveTab] = useState('places');
  const [filters, setFilters] = useState({});
  const [showFilters, setShowFilters] = useState(false);

  if (!city) return null;

  // Filter data based on current filters
  const filteredData = useMemo(() => {
    const filterPlaces = (places) => {
      if (!places) return [];
      return places.filter(place => {
        if (filters.category && filters.category !== place.category) return false;
        if (filters.rating && place.rating < parseFloat(filters.rating.split('+')[0])) return false;
        if (filters.entryFee === 'Free' && place.entryFee !== 'Free') return false;
        if (filters.entryFee === 'Paid' && place.entryFee === 'Free') return false;
        return true;
      });
    };

    const filterRestaurants = (restaurants) => {
      if (!restaurants) return [];
      return restaurants.filter(restaurant => {
        if (filters.cuisine && !restaurant.cuisine?.includes(filters.cuisine)) return false;
        if (filters.priceRange && restaurant.priceRange !== filters.priceRange) return false;
        if (filters.rating && restaurant.rating < parseFloat(filters.rating.split('+')[0])) return false;
        return true;
      });
    };

    const filterVlogs = (vlogs) => {
      if (!vlogs) return [];
      return vlogs.filter(vlog => {
        // Add vlog filtering logic here when vlogs are implemented
        return true;
      });
    };

    return {
      places: filterPlaces(city.places),
      restaurants: filterRestaurants(city.restaurants),
      vlogs: filterVlogs(city.vlogs)
    };
  }, [city, filters]);

  const tabs = [
    { id: 'places', label: '📍 Places', count: filteredData.places?.length || 0 },
    { id: 'restaurants', label: '🍽️ Restaurants', count: filteredData.restaurants?.length || 0 },
    { id: 'vlogs', label: '📹 Vlogs', count: filteredData.vlogs?.length || 0 },
    { id: 'culture', label: '🎉 Culture', count: 1 }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'places':
        return (
          <div className="places-grid">
            {filteredData.places && filteredData.places.length > 0 ? (
              filteredData.places.map((place, index) => (
                <PlaceCard key={index} place={place} />
              ))
            ) : city.places && city.places.length > 0 ? (
              <div className="no-data">
                <p>🔍 No places match your current filters</p>
                <p>Try adjusting your filters to see more results.</p>
              </div>
            ) : (
              <div className="no-data">
                <p>🏛️ No places data available yet</p>
                <p>This feature will be available once the database is connected.</p>
              </div>
            )}
          </div>
        );
      
      case 'restaurants':
        return (
          <div className="restaurants-grid">
            {filteredData.restaurants && filteredData.restaurants.length > 0 ? (
              filteredData.restaurants.map((restaurant, index) => (
                <RestaurantCard key={index} restaurant={restaurant} />
              ))
            ) : city.restaurants && city.restaurants.length > 0 ? (
              <div className="no-data">
                <p>🔍 No restaurants match your current filters</p>
                <p>Try adjusting your filters to see more results.</p>
              </div>
            ) : (
              <div className="no-data">
                <p>🍽️ No restaurant data available yet</p>
                <p>This feature will be available once the database is connected.</p>
              </div>
            )}
          </div>
        );
      
      case 'vlogs':
        return (
          <div className="vlogs-grid">
            {filteredData.vlogs && filteredData.vlogs.length > 0 ? (
              filteredData.vlogs.map((vlog, index) => (
                <VlogCard key={index} vlog={vlog} />
              ))
            ) : city.vlogs && city.vlogs.length > 0 ? (
              <div className="no-data">
                <p>🔍 No vlogs match your current filters</p>
                <p>Try adjusting your filters to see more results.</p>
              </div>
            ) : (
              <div className="no-data">
                <p>📹 No vlogs available yet</p>
                <p>YouTube integration will be available once API keys are configured.</p>
              </div>
            )}
          </div>
        );
      
      case 'culture':
        return (
          <div className="culture-section">
            {city.culture ? (
              <div className="culture-content">
                <div className="culture-item">
                  <h3>🎭 Cultural Heritage</h3>
                  <p>{city.culture.culture || 'Rich cultural traditions and heritage'}</p>
                </div>
                <div className="culture-item">
                  <h3>📖 About {city.name}</h3>
                  <p>{city.culture.description || city.description || `${city.name} is a beautiful city with rich history and culture.`}</p>
                </div>
              </div>
            ) : (
              <div className="no-data">
                <p>🎉 Cultural information will be available soon</p>
              </div>
            )}
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div className="city-info">
      <div className="city-header">
        <div className="city-title">
          <h1>{city.name}</h1>
          <p className="city-description">{city.description}</p>
        </div>
        {city.image && (
          <div className="city-image">
            <img src={city.image} alt={city.name} />
          </div>
        )}
      </div>

      <ResponsiveNav
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={(tabId) => {
          setActiveTab(tabId);
          setFilters({}); // Clear filters when switching tabs
          setShowFilters(false); // Hide filters when switching tabs
        }}
        showFilters={['places', 'restaurants', 'vlogs'].includes(activeTab)}
        onToggleFilters={() => setShowFilters(!showFilters)}
      />

      {/* Show filters for places, restaurants, and vlogs tabs */}
      {['places', 'restaurants', 'vlogs'].includes(activeTab) && showFilters && (
        <FilterBar
          activeTab={activeTab}
          filters={filters}
          onFilterChange={setFilters}
        />
      )}

      <div className="tab-content">
        {renderTabContent()}
      </div>
    </div>
  );
};

export default CityInfo;
