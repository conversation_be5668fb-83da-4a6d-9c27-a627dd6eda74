import './About.css';

const About = () => {
  return (
    <div className="about-page">
      <div className="about-container">
        <header className="about-header">
          <h1>About CityUnfold</h1>
          <p className="about-subtitle">Discover the world, one city at a time</p>
        </header>

        <div className="about-content">
          <section className="about-section">
            <div className="section-icon">🌍</div>
            <h2>Our Mission</h2>
            <p>
              CityUnfold is your ultimate travel companion, designed to help you explore and discover 
              the hidden gems of cities around the world. We believe that every city has a unique story 
              to tell, and we're here to help you unfold those stories.
            </p>
          </section>

          <section className="about-section">
            <div className="section-icon">🎯</div>
            <h2>What We Offer</h2>
            <div className="features-grid">
              <div className="feature-card">
                <h3>🏛️ Top Tourist Places</h3>
                <p>Discover must-visit attractions, monuments, and landmarks with detailed information and ratings.</p>
              </div>
              <div className="feature-card">
                <h3>🍽️ Best Restaurants</h3>
                <p>Find the finest dining experiences, from street food to fine dining, curated for every taste.</p>
              </div>
              <div className="feature-card">
                <h3>📹 Travel Vlogs</h3>
                <p>Watch authentic travel experiences through carefully selected YouTube vlogs from real travelers.</p>
              </div>
              <div className="feature-card">
                <h3>🗺️ Interactive Maps</h3>
                <p>Navigate with ease using integrated Google Maps showing all the important locations.</p>
              </div>
              <div className="feature-card">
                <h3>🎉 Local Culture</h3>
                <p>Immerse yourself in local traditions, festivals, and cultural highlights of each destination.</p>
              </div>
              <div className="feature-card">
                <h3>🔍 Smart Search</h3>
                <p>Find information quickly with our intelligent search and filtering system.</p>
              </div>
            </div>
          </section>

          <section className="about-section">
            <div className="section-icon">💡</div>
            <h2>Why Choose CityUnfold?</h2>
            <ul className="benefits-list">
              <li>
                <strong>Comprehensive Information:</strong> Everything you need to know about a city in one place
              </li>
              <li>
                <strong>Real Traveler Insights:</strong> Authentic experiences shared through vlogs and reviews
              </li>
              <li>
                <strong>Mobile-Friendly:</strong> Access information on-the-go with our responsive design
              </li>
              <li>
                <strong>Always Updated:</strong> Fresh content and up-to-date information about destinations
              </li>
              <li>
                <strong>Easy to Use:</strong> Intuitive interface designed for travelers of all ages
              </li>
            </ul>
          </section>

          <section className="about-section">
            <div className="section-icon">🚀</div>
            <h2>Our Technology</h2>
            <p>
              Built with modern web technologies including React.js, Node.js, Express.js, and MongoDB, 
              CityUnfold delivers a fast, reliable, and seamless user experience. We integrate with 
              YouTube Data API and Google Maps API to provide you with the most comprehensive travel information.
            </p>
          </section>

          <section className="about-section cta-section">
            <h2>Start Your Journey Today</h2>
            <p>Ready to explore? Search for your next destination and let CityUnfold guide your adventure!</p>
            <button 
              className="cta-button"
              onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
            >
              Start Exploring
            </button>
          </section>
        </div>
      </div>
    </div>
  );
};

export default About;
