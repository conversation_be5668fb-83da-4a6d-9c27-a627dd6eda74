import './VlogCard.css';

const VlogCard = ({ vlog }) => {
  const formatViewCount = (count) => {
    if (!count) return '';
    const num = parseInt(count);
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M views`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K views`;
    }
    return `${num} views`;
  };

  const formatDuration = (duration) => {
    if (!duration) return '';
    // Parse ISO 8601 duration format (PT12M34S)
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
    if (!match) return '';
    
    const hours = parseInt(match[1]) || 0;
    const minutes = parseInt(match[2]) || 0;
    const seconds = parseInt(match[3]) || 0;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const formatPublishedDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return '1 day ago';
    if (diffDays < 30) return `${diffDays} days ago`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
    return `${Math.floor(diffDays / 365)} years ago`;
  };

  const handleVideoClick = () => {
    if (vlog.videoId) {
      window.open(`https://www.youtube.com/watch?v=${vlog.videoId}`, '_blank');
    }
  };

  return (
    <div className="vlog-card" onClick={handleVideoClick}>
      <div className="vlog-thumbnail">
        {vlog.thumbnail ? (
          <img src={vlog.thumbnail} alt={vlog.title} />
        ) : (
          <div className="placeholder-thumbnail">
            <span className="video-icon">📹</span>
          </div>
        )}
        {vlog.duration && (
          <div className="video-duration">{formatDuration(vlog.duration)}</div>
        )}
        <div className="play-overlay">
          <div className="play-button">▶️</div>
        </div>
      </div>
      
      <div className="vlog-content">
        <h3 className="vlog-title">{vlog.title}</h3>
        <p className="vlog-channel">{vlog.channelTitle}</p>
        
        <div className="vlog-meta">
          {vlog.viewCount && (
            <span className="view-count">{formatViewCount(vlog.viewCount)}</span>
          )}
          {vlog.publishedAt && (
            <span className="publish-date">{formatPublishedDate(vlog.publishedAt)}</span>
          )}
        </div>
        
        {vlog.description && (
          <p className="vlog-description">
            {vlog.description.length > 100 
              ? `${vlog.description.substring(0, 100)}...` 
              : vlog.description
            }
          </p>
        )}
      </div>
    </div>
  );
};

export default VlogCard;
