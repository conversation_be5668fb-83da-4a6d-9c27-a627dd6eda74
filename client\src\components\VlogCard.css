.vlog-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.vlog-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.vlog-thumbnail {
  position: relative;
  height: 200px;
  overflow: hidden;
  background: #000;
}

.vlog-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.vlog-card:hover .vlog-thumbnail img {
  transform: scale(1.05);
}

.placeholder-thumbnail {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-icon {
  font-size: 3rem;
  opacity: 0.8;
}

.video-duration {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.vlog-card:hover .play-overlay {
  opacity: 1;
}

.play-button {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  transition: transform 0.2s ease;
}

.play-button:hover {
  transform: scale(1.1);
}

.vlog-content {
  padding: 20px;
}

.vlog-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.vlog-channel {
  color: #666;
  font-size: 0.9rem;
  margin: 0 0 10px 0;
  font-weight: 500;
}

.vlog-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 13px;
  color: #999;
  flex-wrap: wrap;
  gap: 10px;
}

.view-count {
  font-weight: 500;
}

.publish-date {
  font-style: italic;
}

.vlog-description {
  color: #666;
  font-size: 0.85rem;
  line-height: 1.5;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Responsive design */
@media (max-width: 768px) {
  .vlog-thumbnail {
    height: 180px;
  }
  
  .vlog-content {
    padding: 15px;
  }
  
  .vlog-title {
    font-size: 1rem;
  }
  
  .vlog-channel {
    font-size: 0.85rem;
  }
  
  .vlog-meta {
    font-size: 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .play-button {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }
}
