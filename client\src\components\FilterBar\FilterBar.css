.filter-bar {
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #e0e0e0;
}

.filter-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #333;
}

.filter-icon {
  font-size: 16px;
}

.active-indicator {
  color: #667eea;
  font-size: 20px;
  line-height: 1;
}

.filter-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.clear-filters-btn {
  background: #ff6b6b;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-filters-btn:hover {
  background: #ff5252;
  transform: translateY(-1px);
}

.expand-btn {
  background: transparent;
  border: 1px solid #ddd;
  color: #666;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
}

.expand-btn:hover {
  background: #f0f0f0;
  border-color: #667eea;
  color: #667eea;
}

.expand-btn.expanded {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.filter-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.filter-content.expanded {
  max-height: 200px;
  padding: 20px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.filter-group:last-child {
  margin-bottom: 0;
}

.filter-label {
  min-width: 100px;
  font-weight: 500;
  color: #555;
  font-size: 14px;
}

.filter-select {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
  font-size: 14px;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.quick-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 15px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e0e0e0;
}

.filter-chip {
  display: flex;
  align-items: center;
  gap: 6px;
  background: #667eea;
  color: white;
  padding: 4px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

.remove-filter {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  line-height: 1;
  transition: background 0.2s ease;
}

.remove-filter:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Responsive design */
@media (max-width: 768px) {
  .filter-header {
    padding: 12px 15px;
  }
  
  .filter-content.expanded {
    padding: 15px;
  }
  
  .filter-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .filter-label {
    min-width: auto;
    font-size: 13px;
  }
  
  .filter-select {
    width: 100%;
  }
  
  .quick-filters {
    padding: 12px 15px;
  }
  
  .clear-filters-btn {
    font-size: 11px;
    padding: 5px 10px;
  }
}
